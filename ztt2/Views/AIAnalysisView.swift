//
//  AIAnalysisView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI分析报告主视图
 * 参考ztt1项目实现，负责展示成员的AI行为分析报告，包含权限检查、加载状态和报告内容
 */
struct AIAnalysisView: View {

    // MARK: - Properties
    let member: Member
    let onDismiss: () -> Void
    let onNavigateToSubscription: (() -> Void)?

    // MARK: - State
    @StateObject private var viewModel = AIAnalysisViewModel()
    @State private var pageAppeared = false
    @State private var selectedReportType: AIReportType = .behaviorAnalysis

    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            topNavigationBar

            // 主要内容
            if viewModel.canGenerateReport(for: member, reportType: selectedReportType).canGenerate {
                if let report = viewModel.currentReport {
                    // 显示报告内容
                    reportContentView(report: report)
                } else if viewModel.isGenerating {
                    // 加载状态
                    loadingView
                } else {
                    // 生成报告按钮
                    generateReportView
                }
            } else {
                // 权限不足视图
                permissionDeniedView
            }
        }
        .background(createBackgroundGradient())
        .navigationBarHidden(true)
        .onAppear {
            setupView()
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.clearError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }

    // MARK: - View Components

    /**
     * 顶部导航栏
     */
    private var topNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: {
                onDismiss()
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 17, weight: .medium))
                    Text("ai_analysis.return".localized)
                        .font(.system(size: 17))
                }
                .foregroundColor(.blue)
            }

            Spacer()

            // 标题
            Text("ai_analysis.title".localized)
                .font(.system(size: 17, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 占位符保持居中
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 17, weight: .medium))
                Text("ai_analysis.return".localized)
                    .font(.system(size: 17))
            }
            .opacity(0)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.white.opacity(0.95))
    }

    /**
     * 背景渐变
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(hex: "#f8f9fa"),
                Color(hex: "#e9ecef")
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea(.all)
    }

    /**
     * 权限不足视图
     */
    private var permissionDeniedView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 权限图标
            Image(systemName: "lock.circle")
                .font(.system(size: 64))
                .foregroundColor(Color(hex: "#FFB84D"))
                .opacity(pageAppeared ? 1.0 : 0.0)
                .scaleEffect(pageAppeared ? 1.0 : 0.8)
                .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: pageAppeared)

            // 权限说明
            VStack(spacing: 12) {
                Text("ai_analysis.permission_title".localized)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(getPermissionStatusDescription())
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            .opacity(pageAppeared ? 1.0 : 0.0)
            .offset(y: pageAppeared ? 0 : 20)
            .animation(.easeOut(duration: 0.6).delay(0.4), value: pageAppeared)

            // 升级按钮
            if let onNavigateToSubscription = onNavigateToSubscription {
                Button(action: {
                    onNavigateToSubscription()
                }) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 16))
                        Text("升级会员")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#FFB84D"),
                                Color(hex: "#FF9500")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: Color(hex: "#FFB84D").opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 20)
                .animation(.easeOut(duration: 0.6).delay(0.6), value: pageAppeared)
            }

            Spacer()
        }
        .padding(.horizontal, 24)
    }

    /**
     * 生成报告视图
     */
    private var generateReportView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 成员信息卡片
            memberInfoCard

            // 报告类型选择器
            reportTypeSelector
                .padding(.top, 8)

            // 生成按钮
            Button(action: {
                Task {
                    await generateSelectedReport()
                }
            }) {
                HStack {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 18))
                    Text("ai_analysis.generate_button".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#74c07f"),
                            Color(hex: "#5da961")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(25)
                .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(!viewModel.isNetworkAvailable)
            .opacity(viewModel.isNetworkAvailable ? 1.0 : 0.6)

            // 网络状态提示
            if !viewModel.isNetworkAvailable {
                Text("ai_analysis.no_network_connection".localized)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }

            // 历史分析记录
            historyReportsSection
                .padding(.top, 20)

            Spacer()
        }
        .padding(.horizontal, 24)
        .opacity(pageAppeared ? 1.0 : 0.0)
        .offset(y: pageAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: pageAppeared)
    }

    /**
     * 成员信息卡片
     */
    private var memberInfoCard: some View {
        VStack(spacing: 16) {
            // 头像和基本信息
            HStack(spacing: 16) {
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .clipShape(Circle())

                VStack(alignment: .leading, spacing: 4) {
                    Text(member.displayName)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("\(member.roleDisplayName) · \(member.age)岁")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()
            }

            // 数据统计
            HStack(spacing: 20) {
                dataStatItem(
                    title: "行为记录",
                    count: member.sortedPointRecords.count,
                    minRequired: 10
                )

                Rectangle()
                    .fill(DesignSystem.Colors.textTertiary)
                    .frame(width: 1, height: 40)

                dataStatItem(
                    title: "成长日记",
                    count: member.sortedGrowthDiaries.count,
                    minRequired: 10
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .opacity(pageAppeared ? 1.0 : 0.0)
        .scaleEffect(pageAppeared ? 1.0 : 0.95)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: pageAppeared)
    }

    /**
     * 数据统计项
     */
    private func dataStatItem(title: String, count: Int, minRequired: Int) -> some View {
        VStack(spacing: 8) {
            Text("\(count)")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(count >= minRequired ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)

            Text(title)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            if count < minRequired {
                Text("需要\(minRequired)条")
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }
        }
        .frame(maxWidth: .infinity)
    }

    /**
     * 报告类型选择器
     */
    private var reportTypeSelector: some View {
        VStack(spacing: 16) {
            Text("ai_analysis.select_report_type".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                // 行为分析报告选项
                reportTypeOption(
                    type: .behaviorAnalysis,
                    title: "行为分析报告",
                    subtitle: "基于积分记录分析行为趋势",
                    icon: "chart.line.uptrend.xyaxis"
                )

                // 成长报告选项
                reportTypeOption(
                    type: .growthReport,
                    title: "成长报告",
                    subtitle: "基于成长日记分析发展状况",
                    icon: "heart.text.square"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .opacity(pageAppeared ? 1.0 : 0.0)
        .scaleEffect(pageAppeared ? 1.0 : 0.95)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: pageAppeared)
    }

    /**
     * 报告类型选项
     */
    private func reportTypeOption(type: AIReportType, title: String, subtitle: String, icon: String) -> some View {
        Button(action: {
            selectedReportType = type
        }) {
            HStack(spacing: 16) {
                // 选择指示器
                Image(systemName: selectedReportType == type ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(selectedReportType == type ? Color(hex: "#74c07f") : DesignSystem.Colors.textTertiary)

                // 图标
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(Color(hex: "#74c07f"))
                    .frame(width: 32)

                // 文本信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    // 数据要求提示
                    let dataCount = type == .behaviorAnalysis ? member.sortedPointRecords.count : member.sortedGrowthDiaries.count
                    if dataCount < 10 {
                        Text("需要至少10条记录（当前：\(dataCount)条）")
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.errorColor)
                    }
                }

                Spacer()
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(selectedReportType == type ? Color(hex: "#74c07f").opacity(0.1) : Color(.systemGray6))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(selectedReportType == type ? Color(hex: "#74c07f") : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    /**
     * 加载视图
     */
    private var loadingView: some View {
        VStack(spacing: 24) {
            Spacer()

            // 加载动画
            ProgressView()
                .scaleEffect(1.5)
                .tint(Color(hex: "#74c07f"))

            Text("ai_analysis.generating".localized)
                .font(.system(size: 16))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Text("ai_analysis.please_wait".localized)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textTertiary)

            Spacer()
        }
    }

    /**
     * 报告内容视图
     */
    private func reportContentView(report: AIAnalysisReport) -> some View {
        VStack(spacing: 0) {
            // 报告详细内容
            AIReportDetailView(report: report)

            // 历史分析记录
            historyReportsSection
        }
        .opacity(pageAppeared ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.6), value: pageAppeared)
    }

    /**
     * 历史分析记录部分
     */
    private var historyReportsSection: some View {
        VStack(spacing: 16) {
            // 分隔线
            Rectangle()
                .fill(DesignSystem.Colors.textTertiary.opacity(0.3))
                .frame(height: 1)
                .padding(.horizontal, 20)

            // 历史记录标题和按钮
            HStack {
                Text("历史分析记录")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                Button(action: {
                    viewModel.showingHistoryReports = true
                }) {
                    HStack(spacing: 4) {
                        Text("查看全部")
                            .font(.system(size: 14))
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12))
                    }
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 20)

            // 最近的历史记录预览
            if viewModel.historyReports.isEmpty {
                // 空状态
                VStack(spacing: 12) {
                    Image(systemName: "doc.text.magnifyingglass")
                        .font(.system(size: 40))
                        .foregroundColor(DesignSystem.Colors.textTertiary)

                    Text("暂无历史记录")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.vertical, 30)
            } else {
                // 显示最近3条记录
                LazyVStack(spacing: 12) {
                    ForEach(Array(viewModel.historyReports.prefix(3)), id: \.id) { report in
                        historyReportRow(report)
                    }
                }
                .padding(.horizontal, 20)
            }

            Spacer(minLength: 20)
        }
        .background(Color.clear)
        .sheet(isPresented: $viewModel.showingHistoryReports) {
            AIAnalysisHistoryView(member: member)
        }
    }

    /**
     * 历史记录行视图
     */
    private func historyReportRow(_ report: AIReport) -> some View {
        HStack(spacing: 12) {
            // 报告类型图标
            Image(systemName: reportTypeIcon(for: report))
                .font(.system(size: 16))
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)

            // 报告信息
            VStack(alignment: .leading, spacing: 4) {
                Text(reportTypeDisplayName(for: report))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(formatReportDate(report.createdAt ?? Date()))
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()

            // 查看按钮
            Image(systemName: "chevron.right")
                .font(.system(size: 12))
                .foregroundColor(DesignSystem.Colors.textTertiary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .onTapGesture {
            // 这里可以添加点击查看详情的逻辑
        }
    }

    // MARK: - Methods

    /**
     * 设置视图
     */
    private func setupView() {
        viewModel.loadHistoryReports(for: member)

        withAnimation(.easeInOut(duration: 0.8)) {
            pageAppeared = true
        }
    }

    /**
     * 生成选中的报告
     */
    private func generateSelectedReport() async {
        switch selectedReportType {
        case .behaviorAnalysis:
            await viewModel.generateBehaviorAnalysisReport(for: member)
        case .growthReport:
            await viewModel.generateGrowthReport(for: member)
        }
    }

    /**
     * 获取权限状态描述
     */
    private func getPermissionStatusDescription() -> String {
        let permissionResult = viewModel.canGenerateReport(for: member, reportType: selectedReportType)
        return permissionResult.reason ?? "AI分析功能需要高级会员权限"
    }

    /**
     * 获取报告类型图标
     */
    private func reportTypeIcon(for report: AIReport) -> String {
        switch report.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }

    /**
     * 获取报告类型显示名称
     */
    private func reportTypeDisplayName(for report: AIReport) -> String {
        switch report.reportType ?? "analysis" {
        case "analysis":
            return "行为分析报告"
        case "growth":
            return "成长报告"
        default:
            return "AI分析报告"
        }
    }

    /**
     * 格式化报告日期
     */
    private func formatReportDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "今天 HH:mm"
        } else if calendar.isDateInYesterday(date) {
            formatter.dateFormat = "昨天 HH:mm"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .year) {
            formatter.dateFormat = "M月d日 HH:mm"
        } else {
            formatter.dateFormat = "yyyy年M月d日"
        }

        return formatter.string(from: date)
    }
}
