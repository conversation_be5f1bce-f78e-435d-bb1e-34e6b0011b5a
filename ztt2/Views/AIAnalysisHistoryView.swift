//
//  AIAnalysisHistoryView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI分析历史记录页面
 * 显示成员的所有AI分析报告历史
 */
struct AIAnalysisHistoryView: View {
    
    // MARK: - Properties
    
    let member: Member
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = AIAnalysisViewModel()
    @State private var selectedReportType: AIReportType? = nil
    @State private var selectedReport: AIReport? = nil
    @State private var showingReportDetail = false
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏
            customNavigationBar

            // 筛选器
            filterSegmentedControl

            // 报告列表
            if filteredReports.isEmpty {
                emptyStateView
            } else {
                reportsList
            }
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f8f9fa"),
                    Color(hex: "#e9ecef")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .fullScreenCover(isPresented: $showingReportDetail) {
            if let report = selectedReport {
                AIReportDetailCoreDataView(aiReport: report)
            }
        }
        .onAppear {
            loadReports()
        }
    }
    
    // MARK: - View Components

    /// 自定义导航栏
    private var customNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: {
                dismiss()
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("返回")
                        .font(.system(size: 16))
                }
                .foregroundColor(.blue)
            }

            Spacer()

            // 标题
            Text("历史分析记录")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 占位符保持平衡
            HStack(spacing: 6) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                Text("返回")
                    .font(.system(size: 16))
            }
            .opacity(0)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color.white)
        .shadow(color: .black.opacity(0.05), radius: 1, x: 0, y: 1)
    }

    /// 筛选器分段控制
    private var filterSegmentedControl: some View {
        VStack(spacing: 0) {
            Picker("报告类型", selection: $selectedReportType) {
                Text("全部").tag(nil as AIReportType?)
                Text("行为分析").tag(AIReportType.behaviorAnalysis as AIReportType?)
                Text("成长报告").tag(AIReportType.growthReport as AIReportType?)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()
            
            Divider()
        }
        .background(Color(.systemGray6))
        .onChange(of: selectedReportType) { _ in
            loadReports()
        }
    }
    
    /// 报告列表
    private var reportsList: some View {
        List {
            ForEach(groupedReports.keys.sorted(by: >), id: \.self) { date in
                Section {
                    ForEach(groupedReports[date] ?? [], id: \.id) { report in
                        reportRowView(report)
                            .onTapGesture {
                                selectedReport = report
                                showingReportDetail = true
                            }
                    }
                } header: {
                    Text(formatSectionDate(date))
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .listStyle(PlainListStyle())
    }
    
    /// 报告行视图
    private func reportRowView(_ report: AIReport) -> some View {
        HStack(spacing: 12) {
            // 报告类型图标
            Image(systemName: reportTypeIcon(for: report))
                .font(.title2)
                .foregroundColor(reportTypeColor(for: report))
                .frame(width: 30)
            
            // 报告信息
            VStack(alignment: .leading, spacing: 4) {
                Text(report.reportTypeDisplayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if let summary = report.inputDataSummary {
                    Text(summary)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Text(report.formattedCreatedAt)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 箭头指示器
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
    }
    
    /// 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("暂无历史报告")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(emptyStateMessage)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Computed Properties
    
    /// 筛选后的报告
    private var filteredReports: [AIReport] {
        if let selectedType = selectedReportType {
            let typeString = selectedType == .behaviorAnalysis ? "analysis" : "growth"
            return viewModel.historyReports.filter { ($0.reportType ?? "analysis") == typeString }
        } else {
            return viewModel.historyReports
        }
    }
    
    /// 按日期分组的报告
    private var groupedReports: [Date: [AIReport]] {
        Dictionary(grouping: filteredReports) { report in
            Calendar.current.startOfDay(for: report.createdAt ?? Date())
        }
    }
    
    /// 空状态消息
    private var emptyStateMessage: String {
        if let selectedType = selectedReportType {
            return "还没有生成过\(selectedType.displayName)\n快去生成第一份报告吧！"
        } else {
            return "还没有生成过AI分析报告\n快去生成第一份报告吧！"
        }
    }
    
    // MARK: - Helper Methods
    
    /// 加载报告
    private func loadReports() {
        viewModel.loadHistoryReports(for: member, reportType: selectedReportType)
    }
    
    /// 获取报告类型图标
    private func reportTypeIcon(for report: AIReport) -> String {
        switch report.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }
    
    /// 获取报告类型颜色
    private func reportTypeColor(for report: AIReport) -> Color {
        switch report.reportType ?? "analysis" {
        case "analysis":
            return .blue
        case "growth":
            return .green
        default:
            return .gray
        }
    }
    
    /// 格式化分组日期
    private func formatSectionDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        
        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            return "今天"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .year) {
            formatter.dateFormat = "M月d日"
            return formatter.string(from: date)
        } else {
            formatter.dateFormat = "yyyy年M月d日"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Preview

#if DEBUG
struct AIAnalysisHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建一个示例成员用于预览
        let context = PersistenceController.preview.container.viewContext
        let member = Member(context: context)
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        
        return AIAnalysisHistoryView(member: member)
    }
}
#endif
